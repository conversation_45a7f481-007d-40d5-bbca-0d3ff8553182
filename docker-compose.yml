services:

  mysql:
    container_name: mysql
    image: mariadb:10.5
    platform: linux/arm64/v8
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-appdb}
      MYSQL_USER: ${MYSQL_USER:-appuser}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-userpassword}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  php:
    container_name: php
    build: .
    volumes:
      - ./html:/var/www/html
    depends_on:
      - mysql

  nginx:
    container_name: nginx
    image: nginx:stable-alpine
    ports:
      - "${PORT:-80}:80"
    volumes:
      - ./html:/var/www/html
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php

volumes:
  mysql_data: