# Dockerfile
FROM php:8.1-fpm

# Установка зависимостей для расширений PHP и MySQL клиент (включая mysqldump)
RUN apt-get update && apt-get install -y \
    libzip-dev \
    zip \
    unzip \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libwebp-dev \
    libxml2-dev \
    libicu-dev \
    libonig-dev \
    libcurl4-openssl-dev \
    default-mysql-client \
    && rm -rf /var/lib/apt/lists/*

# Настройка расширения GD
RUN docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp

# Установка расширений PHP
RUN docker-php-ext-install \
    pdo \
    pdo_mysql \
    mysqli \
    zip \
    gd \
    exif \
    bcmath \
    intl \
    opcache \
    calendar \
    mbstring \
    xml \
    curl \
    soap

# Оптимизация PHP для production
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Скачивание и установка последней версии WordPress
#Это монтирует локальную директорию ./html поверх /var/www/html в контейнере. Когда происходит такое монтирование, содержимое локальной директории имеет приоритет над файлами, которые были помещены в эту директорию во время сборки образа.
WORKDIR /var/www/html

# Очистка кеша
RUN apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*